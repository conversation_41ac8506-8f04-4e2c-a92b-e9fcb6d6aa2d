"""HTTP client for SurePass KYC API"""

import httpx
from typing import Dict, Any, Optional, Union
import json
from pathlib import Path

from config import BASE_URL, DEFAULT_HEADERS, MULTIPART_HEADERS
from models import KYCResponse, APIError


class KYCClient:
    """HTTP client for KYC API operations"""
    
    def __init__(self, timeout: int = 30):
        self.base_url = BASE_URL
        self.timeout = timeout
        self.client = httpx.Client(timeout=timeout)
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.client.close()
    
    def _prepare_headers(self, authorization_token: Optional[str] = None,
                        is_multipart: bool = False) -> Dict[str, str]:
        """Prepare headers for API request"""
        if is_multipart:
            headers = {}  # Let httpx set Content-Type for multipart
        else:
            headers = DEFAULT_HEADERS.copy()

        if authorization_token:
            if authorization_token.startswith('Bearer '):
                headers["Authorization"] = authorization_token
            else:
                headers["Authorization"] = f"Bearer {authorization_token}"

        return headers
    
    async def post_json(self, endpoint: str, data: Dict[str, Any],
                       authorization_token: Optional[str] = None) -> KYCResponse:
        """Make a POST request with JSON data"""
        url = f"{self.base_url}{endpoint}"
        headers = self._prepare_headers(authorization_token)
        
        try:
            response = self.client.post(url, json=data, headers=headers)
            return self._handle_response(response)
        except httpx.RequestError as e:
            raise APIError(f"Request failed: {str(e)}")
    
    async def post_form(self, endpoint: str, files: Dict[str, Any],
                       data: Optional[Dict[str, str]] = None,
                       authorization_token: Optional[str] = None) -> KYCResponse:
        """Make a POST request with form data (file upload)"""
        url = f"{self.base_url}{endpoint}"
        headers = self._prepare_headers(authorization_token, is_multipart=True)
        
        try:
            # Prepare files for upload
            prepared_files = {}
            for key, file_path in files.items():
                if isinstance(file_path, str):
                    file_path = Path(file_path)
                if file_path.exists():
                    prepared_files[key] = open(file_path, 'rb')
                else:
                    raise APIError(f"File not found: {file_path}")
            
            response = self.client.post(url, files=prepared_files, 
                                      data=data or {}, headers=headers)
            
            # Close file handles
            for file_handle in prepared_files.values():
                file_handle.close()
            
            return self._handle_response(response)
        except httpx.RequestError as e:
            raise APIError(f"Request failed: {str(e)}")
    
    def _handle_response(self, response: httpx.Response) -> KYCResponse:
        """Handle HTTP response"""
        try:
            if response.status_code == 200:
                data = response.json()
                return KYCResponse(success=True, data=data, status_code=response.status_code)
            else:
                error_data = response.text
                try:
                    error_json = response.json()
                    error_message = error_json.get('message', error_data)
                except:
                    error_message = error_data
                
                return KYCResponse(
                    success=False, 
                    error=f"API Error ({response.status_code}): {error_message}",
                    status_code=response.status_code
                )
        except json.JSONDecodeError:
            return KYCResponse(
                success=False,
                error=f"Invalid JSON response: {response.text}",
                status_code=response.status_code
            )
    
    def close(self):
        """Close the HTTP client"""
        self.client.close()
