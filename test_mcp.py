#!/usr/bin/env python3
"""
Comprehensive test script for KYC MCP Server
Tests all components and provides real-time functionality verification
"""

import asyncio
import json
import os
import sys
import logging
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MCPTester:
    """Comprehensive tester for KYC MCP Server"""
    
    def __init__(self):
        self.passed_tests = 0
        self.total_tests = 0
        self.test_results = []
    
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            status = "✓ PASS"
            logger.info(f"{status}: {test_name}")
        else:
            status = "✗ FAIL"
            logger.error(f"{status}: {test_name} - {message}")
        
        self.test_results.append({
            "test": test_name,
            "passed": passed,
            "message": message
        })
    
    async def test_imports(self) -> bool:
        """Test that all required modules can be imported"""
        try:
            from tools import KYCTools
            from kyc_client import KYCClient
            from models import KYCResponse
            from config import BASE_URL, ENDPOINTS
            import kyc_mcp_server
            
            self.log_test("Module Imports", True)
            return True
        except Exception as e:
            self.log_test("Module Imports", False, str(e))
            return False
    
    async def test_configuration(self) -> bool:
        """Test configuration loading"""
        try:
            from config import BASE_URL, ENDPOINTS
            
            # Check base URL
            if not BASE_URL or not BASE_URL.startswith('http'):
                raise ValueError("Invalid BASE_URL")
            
            # Check endpoints
            if not ENDPOINTS or not isinstance(ENDPOINTS, dict):
                raise ValueError("Invalid ENDPOINTS configuration")
            
            # Check some key endpoints exist
            required_endpoints = ['tan', 'voter_id', 'bank_verification']
            for endpoint in required_endpoints:
                if endpoint not in ENDPOINTS:
                    raise ValueError(f"Missing endpoint: {endpoint}")
            
            self.log_test("Configuration", True, f"Base URL: {BASE_URL}, Endpoints: {len(ENDPOINTS)}")
            return True
        except Exception as e:
            self.log_test("Configuration", False, str(e))
            return False
    
    async def test_kyc_client(self) -> bool:
        """Test KYC client initialization"""
        try:
            from kyc_client import KYCClient
            
            client = KYCClient()
            
            # Test client has required methods
            required_methods = ['post_json', 'post_form']
            for method in required_methods:
                if not hasattr(client, method):
                    raise ValueError(f"Missing method: {method}")
            
            self.log_test("KYC Client", True)
            return True
        except Exception as e:
            self.log_test("KYC Client", False, str(e))
            return False
    
    async def test_models(self) -> bool:
        """Test data models"""
        try:
            from models import KYCResponse
            
            # Test model creation
            response = KYCResponse(
                success=True,
                status_code=200,
                message="Test",
                data={"test": "data"}
            )
            
            if not response.success or response.status_code != 200:
                raise ValueError("Model creation failed")
            
            self.log_test("Data Models", True)
            return True
        except Exception as e:
            self.log_test("Data Models", False, str(e))
            return False
    
    async def test_tools_initialization(self) -> bool:
        """Test KYC tools initialization"""
        try:
            from tools import KYCTools
            
            kyc_tools = KYCTools()
            tools = kyc_tools.get_tools()
            
            if not tools or len(tools) == 0:
                raise ValueError("No tools found")
            
            # Check some essential tools exist
            tool_names = [tool.name for tool in tools]
            essential_tools = ['verify_tan', 'verify_voter_id', 'verify_bank_account']
            
            for tool_name in essential_tools:
                if tool_name not in tool_names:
                    raise ValueError(f"Missing essential tool: {tool_name}")
            
            self.log_test("Tools Initialization", True, f"Found {len(tools)} tools")
            return True
        except Exception as e:
            self.log_test("Tools Initialization", False, str(e))
            return False
    
    async def test_tool_schemas(self) -> bool:
        """Test tool schemas are valid"""
        try:
            from tools import KYCTools
            
            kyc_tools = KYCTools()
            tools = kyc_tools.get_tools()
            
            for tool in tools:
                # Check required fields
                if not tool.name or not tool.description:
                    raise ValueError(f"Tool missing name or description: {tool}")
                
                # Check input schema
                if not hasattr(tool, 'inputSchema') or not tool.inputSchema:
                    raise ValueError(f"Tool missing input schema: {tool.name}")
                
                # Check schema has properties
                if 'properties' not in tool.inputSchema:
                    raise ValueError(f"Tool schema missing properties: {tool.name}")
            
            self.log_test("Tool Schemas", True, f"Validated {len(tools)} tool schemas")
            return True
        except Exception as e:
            self.log_test("Tool Schemas", False, str(e))
            return False
    
    async def test_tool_execution_error_handling(self) -> bool:
        """Test tool execution error handling"""
        try:
            from tools import KYCTools
            
            kyc_tools = KYCTools()
            
            # Test with invalid tool name
            result = await kyc_tools.execute_tool("invalid_tool_name", {})
            
            if not result or len(result) == 0:
                raise ValueError("No result returned for invalid tool")
            
            # Check error message is returned
            if "Unknown tool" not in result[0].text:
                raise ValueError("Error handling not working correctly")
            
            self.log_test("Tool Error Handling", True)
            return True
        except Exception as e:
            self.log_test("Tool Error Handling", False, str(e))
            return False
    
    async def test_mcp_server_initialization(self) -> bool:
        """Test MCP server can be initialized"""
        try:
            import kyc_mcp_server

            # Check server is created
            if not hasattr(kyc_mcp_server, 'server'):
                raise ValueError("MCP server not found")

            # Check server has the expected name
            server = kyc_mcp_server.server
            if server.name != "kyc-verification-server":
                raise ValueError(f"Unexpected server name: {server.name}")

            # Check KYC tools are initialized
            if not hasattr(kyc_mcp_server, 'kyc_tools'):
                raise ValueError("KYC tools not initialized")

            self.log_test("MCP Server Initialization", True)
            return True
        except Exception as e:
            self.log_test("MCP Server Initialization", False, str(e))
            return False
    
    async def test_real_api_call(self) -> bool:
        """Test a real API call (TAN verification - no auth required)"""
        try:
            from tools import KYCTools
            
            kyc_tools = KYCTools()
            
            # Test TAN verification (this endpoint doesn't require auth)
            args = {"id_number": "RTKT06731E"}  # Example TAN from API docs
            
            result = await kyc_tools.execute_tool("verify_tan", args)
            
            if not result or len(result) == 0:
                raise ValueError("No result returned from API call")
            
            # Check we got some response (even if it's an error due to demo data)
            response_text = result[0].text
            if not response_text:
                raise ValueError("Empty response from API")
            
            self.log_test("Real API Call (TAN)", True, "API call successful")
            return True
        except Exception as e:
            self.log_test("Real API Call (TAN)", False, str(e))
            return False
    
    async def run_all_tests(self) -> bool:
        """Run all tests"""
        print("=" * 60)
        print("KYC MCP Server - Comprehensive Test Suite")
        print("=" * 60)
        
        tests = [
            ("Module Imports", self.test_imports),
            ("Configuration", self.test_configuration),
            ("KYC Client", self.test_kyc_client),
            ("Data Models", self.test_models),
            ("Tools Initialization", self.test_tools_initialization),
            ("Tool Schemas", self.test_tool_schemas),
            ("Tool Error Handling", self.test_tool_execution_error_handling),
            ("MCP Server Initialization", self.test_mcp_server_initialization),
            ("Real API Call", self.test_real_api_call),
        ]
        
        print(f"\nRunning {len(tests)} tests...\n")
        
        for test_name, test_func in tests:
            print(f"Running: {test_name}...")
            try:
                await test_func()
            except Exception as e:
                self.log_test(test_name, False, f"Test exception: {str(e)}")
        
        # Print results
        print("\n" + "=" * 60)
        print("TEST RESULTS")
        print("=" * 60)
        
        for result in self.test_results:
            status = "✓" if result["passed"] else "✗"
            print(f"{status} {result['test']}")
            if result["message"]:
                print(f"  {result['message']}")
        
        print(f"\nSummary: {self.passed_tests}/{self.total_tests} tests passed")
        
        if self.passed_tests == self.total_tests:
            print("\n🎉 All tests passed! The KYC MCP Server is working correctly.")
            print("\nNext steps:")
            print("1. Run the MCP server: python kyc_mcp_server.py")
            print("2. Configure Claude Desktop (see README.md)")
            print("3. Add your SurePass API credentials for full functionality")
        else:
            print(f"\n⚠️  {self.total_tests - self.passed_tests} test(s) failed.")
            print("Please check the error messages above and fix the issues.")
        
        return self.passed_tests == self.total_tests


async def main():
    """Main test runner"""
    tester = MCPTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n" + "=" * 60)
        print("CLAUDE DESKTOP INTEGRATION GUIDE")
        print("=" * 60)
        print("\nTo connect this MCP server to Claude Desktop:")
        print("\n1. Find your Claude Desktop config file:")
        print("   Windows: %APPDATA%\\Claude\\claude_desktop_config.json")
        print("   macOS: ~/Library/Application Support/Claude/claude_desktop_config.json")
        print("   Linux: ~/.config/Claude/claude_desktop_config.json")
        
        print("\n2. Add this configuration:")
        config = {
            "mcpServers": {
                "kyc-verification": {
                    "command": "python",
                    "args": [os.path.abspath("kyc_mcp_server.py")],
                    "env": {
                        "SUREPASS_API_TOKEN": "your_token_here",
                        "SUREPASS_CUSTOMER_ID": "your_customer_id_here"
                    }
                }
            }
        }
        print(json.dumps(config, indent=2))
        
        print("\n3. Replace 'your_token_here' and 'your_customer_id_here' with your actual credentials")
        print("4. Restart Claude Desktop")
        print("\nThe MCP server will then be available in Claude Desktop!")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
