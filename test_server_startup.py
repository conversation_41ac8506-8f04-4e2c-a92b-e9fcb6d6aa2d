#!/usr/bin/env python3
"""
Test script to verify the MCP server can start properly
"""

import subprocess
import sys
import time
import os

def test_server_startup():
    """Test if the MCP server can start without errors"""
    print("Testing MCP Server Startup...")
    print("=" * 50)
    
    # Set environment variable
    env = os.environ.copy()
    env["SUREPASS_API_TOKEN"] = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************.hXGP7wRAd6hZN12H_LaYQdTdUAXxfir5um3UYkSWGgs"
    
    try:
        # Start the server process
        print("Starting MCP server...")
        process = subprocess.Popen(
            [sys.executable, "kyc_mcp_server.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            env=env,
            text=True
        )
        
        # Send a simple initialization message
        init_message = '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {"roots": {"listChanged": true}, "sampling": {}}, "clientInfo": {"name": "test", "version": "1.0.0"}}}\n'
        
        print("Sending initialization message...")
        process.stdin.write(init_message)
        process.stdin.flush()
        
        # Wait a bit for response
        time.sleep(2)
        
        # Check if process is still running
        if process.poll() is None:
            print("✓ Server started successfully and is running")
            
            # Try to get a response
            try:
                stdout, stderr = process.communicate(timeout=3)
                if stdout:
                    print("✓ Server responded to initialization")
                    print(f"Response: {stdout[:200]}...")
                else:
                    print("⚠ Server started but no response received")
            except subprocess.TimeoutExpired:
                print("✓ Server is running (timeout waiting for response is normal)")
                process.terminate()
                
            return True
        else:
            # Process exited
            stdout, stderr = process.communicate()
            print("✗ Server failed to start")
            if stderr:
                print(f"Error: {stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Error testing server: {e}")
        return False
    finally:
        # Make sure process is terminated
        try:
            if process.poll() is None:
                process.terminate()
                process.wait(timeout=5)
        except:
            pass

if __name__ == "__main__":
    success = test_server_startup()
    if success:
        print("\n🎉 MCP Server startup test PASSED!")
        print("\nThe server is ready for Claude Desktop integration.")
        print("\nNext steps:")
        print("1. Update your Claude Desktop config file")
        print("2. Restart Claude Desktop")
        print("3. Test the integration in Claude")
    else:
        print("\n❌ MCP Server startup test FAILED!")
        print("\nPlease check the error messages above.")
    
    sys.exit(0 if success else 1)
