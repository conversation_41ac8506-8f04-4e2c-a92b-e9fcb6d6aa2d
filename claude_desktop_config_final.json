{"darkMode": "light", "scale": 0, "locale": "en-US", "mcpServers": {"kyc-verification": {"command": "python", "args": ["d:\\Abans\\kyc verification mcp\\kyc_mcp_server.py"], "env": {"SUREPASS_API_TOKEN": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************.hXGP7wRAd6hZN12H_LaYQdTdUAXxfir5um3UYkSWGgs"}, "disabled": false, "autoApprove": []}}}