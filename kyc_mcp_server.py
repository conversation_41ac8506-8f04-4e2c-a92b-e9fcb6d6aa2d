#!/usr/bin/env python3
"""
KYC Verification MCP Server

A Model Context Protocol server that provides KYC (Know Your Customer) verification tools
using the SurePass API. This server implements various document verification, OCR,
face verification, and other KYC-related services.
"""

import json
import logging
from typing import Any

from mcp.server.fastmcp import FastMCP

from tools import KYCTools

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("kyc-mcp-server")

# Create the FastMCP server
mcp = FastMCP("kyc-verification-server")

# Initialize KYC tools
kyc_tools = KYCTools()


# Register all KYC tools with FastMCP
tools = kyc_tools.get_tools()
for tool in tools:
    @mcp.tool(name=tool.name, description=tool.description)
    async def kyc_tool(**kwargs) -> str:
        """Execute KYC tool"""
        # Get the actual tool name from the current function context
        import inspect
        frame = inspect.currentframe()
        tool_name = frame.f_locals.get('tool_name', tool.name)

        try:
            result = await kyc_tools.execute_tool(tool_name, kwargs)
            return result[0].text if result else "No result"
        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {str(e)}")
            return f"Error: {str(e)}"

# Add resources
@mcp.resource("kyc://api/documentation")
def get_documentation() -> str:
    """Get KYC API documentation"""
    return """
KYC Verification MCP Server Documentation

This server provides access to various KYC (Know Your Customer) verification services
through the SurePass API. Available services include:

DOCUMENT VERIFICATION:
- TAN verification
- Voter ID verification
- Driving License verification
- Passport verification
- Aadhaar verification and OTP generation
- PAN card verification

BANK VERIFICATION:
- Bank account verification
- UPI ID verification
- IFSC code validation

CORPORATE VERIFICATION:
- GSTIN verification
- Company CIN verification
- Director details
- Udyog Aadhaar verification

OCR SERVICES:
- PAN card OCR
- Passport OCR
- License OCR
- Voter ID OCR
- GST certificate OCR
- ITR document OCR
- Cheque OCR

FACE VERIFICATION:
- Face matching between selfie and ID
- Face liveness detection
- Face extraction
- Background removal

UTILITY SERVICES:
- Electricity bill verification
- Telecom verification
- Email verification
- Name matching

LEGAL SERVICES:
- Court case searches
- PEP (Politically Exposed Person) checks
- CKYC searches

FINANCIAL SERVICES:
- ITR compliance checks
- TDS verification
- Credit report generation
- ESIC details

VEHICLE SERVICES:
- RC (Registration Certificate) verification
- RC to mobile number mapping

Usage:
Each tool requires specific parameters as documented in the tool schemas.
Most tools require authorization tokens for API access.
File-based tools (OCR, face verification) require valid file paths.
"""

@mcp.resource("kyc://api/endpoints")
def get_endpoints() -> str:
    """Get API endpoints list"""
    from config import ENDPOINTS
    import json
    return json.dumps(ENDPOINTS, indent=2)


if __name__ == "__main__":
    mcp.run()
