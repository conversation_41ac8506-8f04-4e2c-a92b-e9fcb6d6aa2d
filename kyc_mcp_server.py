#!/usr/bin/env python3
"""
KYC Verification MCP Server

A Model Context Protocol server that provides KYC (Know Your Customer) verification tools
using the SurePass API. This server implements various document verification, OCR,
face verification, and other KYC-related services.
"""

import json
import logging
from typing import Any

from mcp.server.fastmcp import FastMC<PERSON>

from kyc_client import K<PERSON><PERSON>lient
from config import ENDPOINTS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("kyc-mcp-server")

# Create the FastMCP server
mcp = FastMCP("kyc-verification-server")

# Initialize KYC client
kyc_client = KYCClient()

# Define individual tools using FastMCP decorators
@mcp.tool()
async def verify_tan(id_number: str) -> str:
    """Verify TAN (Tax Deduction Account Number)

    Args:
        id_number: TAN number to verify
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(ENDPOINTS["tan"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying TAN: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def verify_voter_id(id_number: str, authorization_token: str = None) -> str:
    """Verify Voter ID

    Args:
        id_number: Voter ID number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(
            ENDPOINTS["voter_id"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying Voter ID: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def verify_driving_license(id_number: str, dob: str) -> str:
    """Verify Driving License

    Args:
        id_number: License number
        dob: Date of birth (YYYY-MM-DD)
    """
    try:
        data = {"id_number": id_number, "dob": dob}
        response = await kyc_client.post_json(ENDPOINTS["driving_license"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying Driving License: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def verify_passport(id_number: str, dob: str) -> str:
    """Verify Passport details

    Args:
        id_number: Passport file number
        dob: Date of birth (YYYY-MM-DD)
    """
    try:
        data = {"id_number": id_number, "dob": dob}
        response = await kyc_client.post_json(ENDPOINTS["passport"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying Passport: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def verify_bank_account(id_number: str, ifsc: str, authorization_token: str = None) -> str:
    """Verify bank account details

    Args:
        id_number: Account number
        ifsc: IFSC code
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number, "ifsc": ifsc, "ifsc_details": True}
        response = await kyc_client.post_json(
            ENDPOINTS["bank_verification"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying Bank Account: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def verify_gstin(id_number: str, authorization_token: str = None) -> str:
    """Verify GSTIN details

    Args:
        id_number: GSTIN number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(
            ENDPOINTS["gstin"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying GSTIN: {str(e)}")
        return f"Error: {str(e)}"

# Add resources
@mcp.resource("kyc://api/documentation")
def get_documentation() -> str:
    """Get KYC API documentation"""
    return """
KYC Verification MCP Server Documentation

This server provides access to various KYC (Know Your Customer) verification services
through the SurePass API. Available services include:

DOCUMENT VERIFICATION:
- TAN verification
- Voter ID verification
- Driving License verification
- Passport verification
- Aadhaar verification and OTP generation
- PAN card verification

BANK VERIFICATION:
- Bank account verification
- UPI ID verification
- IFSC code validation

CORPORATE VERIFICATION:
- GSTIN verification
- Company CIN verification
- Director details
- Udyog Aadhaar verification

Usage:
Each tool requires specific parameters as documented in the tool schemas.
Most tools require authorization tokens for API access.
File-based tools (OCR, face verification) require valid file paths.
"""

@mcp.resource("kyc://api/endpoints")
def get_endpoints() -> str:
    """Get API endpoints list"""
    return json.dumps(ENDPOINTS, indent=2)


if __name__ == "__main__":
    mcp.run()
