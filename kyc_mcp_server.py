#!/usr/bin/env python3
"""
KYC Verification MCP Server

A Model Context Protocol server that provides KYC (Know Your Customer) verification tools
using the SurePass API. This server implements various document verification, OCR, 
face verification, and other KYC-related services.
"""

import asyncio
import logging
from typing import Any, Sequence

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource
)
import mcp.server.stdio

from tools import KYCTools

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("kyc-mcp-server")

# Create the MCP server
server = Server("kyc-verification-server")

# Initialize KYC tools
kyc_tools = KYCTools()


@server.list_tools()
async def handle_list_tools() -> list[Tool]:
    """List available KYC verification tools"""
    return kyc_tools.get_tools()


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict[str, Any] | None) -> Sequence[TextContent | ImageContent | EmbeddedResource]:
    """Handle tool execution requests"""
    if arguments is None:
        arguments = {}
    
    logger.info(f"Executing tool: {name} with arguments: {arguments}")
    
    try:
        result = await kyc_tools.execute_tool(name, arguments)
        return result
    except Exception as e:
        logger.error(f"Error executing tool {name}: {str(e)}")
        return [TextContent(type="text", text=f"Error: {str(e)}")]


@server.list_resources()
async def handle_list_resources() -> list[Resource]:
    """List available resources"""
    return [
        Resource(
            uri="kyc://api/documentation",
            name="KYC API Documentation",
            description="Documentation for available KYC verification services",
            mimeType="text/plain"
        ),
        Resource(
            uri="kyc://api/endpoints",
            name="API Endpoints",
            description="List of all available API endpoints",
            mimeType="application/json"
        )
    ]


@server.read_resource()
async def handle_read_resource(uri: str) -> str:
    """Read resource content"""
    if uri == "kyc://api/documentation":
        return """
KYC Verification MCP Server Documentation

This server provides access to various KYC (Know Your Customer) verification services
through the SurePass API. Available services include:

DOCUMENT VERIFICATION:
- TAN verification
- Voter ID verification  
- Driving License verification
- Passport verification
- Aadhaar verification and OTP generation
- PAN card verification

BANK VERIFICATION:
- Bank account verification
- UPI ID verification
- IFSC code validation

CORPORATE VERIFICATION:
- GSTIN verification
- Company CIN verification
- Director details
- Udyog Aadhaar verification

OCR SERVICES:
- PAN card OCR
- Passport OCR
- License OCR
- Voter ID OCR
- GST certificate OCR
- ITR document OCR
- Cheque OCR

FACE VERIFICATION:
- Face matching between selfie and ID
- Face liveness detection
- Face extraction
- Background removal

UTILITY SERVICES:
- Electricity bill verification
- Telecom verification
- Email verification
- Name matching

LEGAL SERVICES:
- Court case searches
- PEP (Politically Exposed Person) checks
- CKYC searches

FINANCIAL SERVICES:
- ITR compliance checks
- TDS verification
- Credit report generation
- ESIC details

VEHICLE SERVICES:
- RC (Registration Certificate) verification
- RC to mobile number mapping

Usage:
Each tool requires specific parameters as documented in the tool schemas.
Most tools require authorization tokens for API access.
File-based tools (OCR, face verification) require valid file paths.
"""
    
    elif uri == "kyc://api/endpoints":
        from config import ENDPOINTS
        import json
        return json.dumps(ENDPOINTS, indent=2)
    
    else:
        raise ValueError(f"Unknown resource: {uri}")


async def main():
    """Main entry point for the MCP server"""
    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="kyc-verification-server",
                server_version="1.0.0",
                capabilities=server.get_capabilities(),
            ),
        )


if __name__ == "__main__":
    asyncio.run(main())
