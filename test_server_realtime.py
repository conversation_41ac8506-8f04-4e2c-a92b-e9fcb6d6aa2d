#!/usr/bin/env python3
"""
Real-time test for KYC MCP Server
Tests the server's ability to handle MCP protocol requests
"""

import asyncio
import json
import sys
from io import StringIO
from unittest.mock import AsyncMock, patch

async def test_mcp_server_realtime():
    """Test the MCP server in real-time"""
    print("Testing KYC MCP Server Real-time Functionality")
    print("=" * 50)
    
    try:
        # Import the server
        import kyc_mcp_server
        
        print("✓ MCP Server imported successfully")
        
        # Test tool listing
        tools = await kyc_mcp_server.handle_list_tools()
        print(f"✓ Found {len(tools)} tools available")
        
        # Test a few key tools
        tool_names = [tool.name for tool in tools]
        essential_tools = ['verify_tan', 'verify_voter_id', 'verify_bank_account']
        
        for tool_name in essential_tools:
            if tool_name in tool_names:
                print(f"✓ Essential tool '{tool_name}' is available")
            else:
                print(f"✗ Essential tool '{tool_name}' is missing")
                return False
        
        # Test resource listing
        resources = await kyc_mcp_server.handle_list_resources()
        print(f"✓ Found {len(resources)} resources available")
        
        # Test resource reading
        doc_resource = await kyc_mcp_server.handle_read_resource("kyc://api/documentation")
        if doc_resource and "KYC Verification MCP Server Documentation" in doc_resource:
            print("✓ Documentation resource accessible")
        else:
            print("✗ Documentation resource not accessible")
            return False
        
        # Test tool execution (with invalid tool for error handling)
        result = await kyc_mcp_server.handle_call_tool("invalid_tool", {})
        if result and len(result) > 0 and "Unknown tool" in result[0].text:
            print("✓ Tool error handling works correctly")
        else:
            print("✗ Tool error handling not working")
            print(f"  Got: {result[0].text if result else 'No result'}")
            return False
        
        # Test actual tool execution (TAN verification)
        print("\nTesting real API call...")
        tan_result = await kyc_mcp_server.handle_call_tool("verify_tan", {"id_number": "RTKT06731E"})
        if tan_result and len(tan_result) > 0:
            print("✓ TAN verification tool executed successfully")
            print(f"  Response preview: {tan_result[0].text[:100]}...")
        else:
            print("✗ TAN verification tool failed")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 All real-time tests passed!")
        print("The KYC MCP Server is working correctly and ready for use.")
        
        return True
        
    except Exception as e:
        print(f"✗ Real-time test failed: {str(e)}")
        return False

async def main():
    """Main test runner"""
    success = await test_mcp_server_realtime()
    
    if success:
        print("\n" + "=" * 50)
        print("READY FOR CLAUDE DESKTOP INTEGRATION")
        print("=" * 50)
        print("\nYour KYC MCP Server is fully functional!")
        print("\nTo connect to Claude Desktop:")
        print("1. Open Claude Desktop configuration file")
        print("2. Add the server configuration (see README.md)")
        print("3. Restart Claude Desktop")
        print("4. The KYC tools will be available in Claude!")
        
        print("\nExample Claude Desktop config:")
        config = {
            "mcpServers": {
                "kyc-verification": {
                    "command": "python",
                    "args": ["D:\\Abans\\kyc verification mcp\\kyc_mcp_server.py"],
                    "env": {
                        "SUREPASS_API_TOKEN": "your_token_here",
                        "SUREPASS_CUSTOMER_ID": "your_customer_id_here"
                    }
                }
            }
        }
        print(json.dumps(config, indent=2))
    else:
        print("\n⚠️ Some tests failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
